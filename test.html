<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>测试GeoJSON加载</title>
</head>
<body>
    <h1>GeoJSON数据测试</h1>
    <div id="status">正在加载数据...</div>
    <div id="info"></div>

    <script>
        async function testGeoJSON() {
            try {
                console.log('开始测试GeoJSON加载...');
                
                const response = await fetch('./flood_data_wgs84.geojson');
                console.log('Response status:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                console.log('数据加载成功:', data);
                
                document.getElementById('status').textContent = '数据加载成功！';
                
                const info = document.getElementById('info');
                info.innerHTML = `
                    <h2>数据信息:</h2>
                    <p>要素数量: ${data.features.length}</p>
                    <p>坐标系统: ${data.crs ? data.crs.properties.name : '未指定'}</p>
                    <p>第一个要素的属性: ${Object.keys(data.features[0].properties).length} 个</p>
                    <p>时间属性数量: ${Object.keys(data.features[0].properties).filter(key => key.startsWith('depth_')).length}</p>
                    <h3>第一个要素的坐标:</h3>
                    <p>${JSON.stringify(data.features[0].geometry.coordinates[0][0])}</p>
                `;
                
            } catch (error) {
                console.error('加载失败:', error);
                document.getElementById('status').textContent = '数据加载失败: ' + error.message;
            }
        }
        
        testGeoJSON();
    </script>
</body>
</html>
