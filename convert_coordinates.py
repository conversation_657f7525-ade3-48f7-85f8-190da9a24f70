import json
import pyproj
from pyproj import Transformer

def convert_geojson_coordinates():
    """
    将GeoJSON文件从EPSG:4545坐标系转换为WGS84 (EPSG:4326)
    """
    
    # 创建坐标转换器
    # EPSG:4545 是中国的某个投影坐标系
    # EPSG:4326 是WGS84地理坐标系 (经纬度)
    transformer = Transformer.from_crs("EPSG:4545", "EPSG:4326", always_xy=True)
    
    # 读取原始GeoJSON文件
    with open('flood_data_prepared.geojson', 'r', encoding='utf-8') as f:
        geojson_data = json.load(f)
    
    print(f"原始数据包含 {len(geojson_data['features'])} 个要素")
    
    # 转换每个要素的坐标
    for i, feature in enumerate(geojson_data['features']):
        if feature['geometry']['type'] == 'Polygon':
            # 转换多边形坐标
            new_coordinates = []
            for ring in feature['geometry']['coordinates']:
                new_ring = []
                for coord in ring:
                    # 转换坐标 (x, y) -> (lon, lat)
                    lon, lat = transformer.transform(coord[0], coord[1])
                    new_ring.append([lon, lat])
                new_coordinates.append(new_ring)
            feature['geometry']['coordinates'] = new_coordinates
        
        if i % 100 == 0:
            print(f"已处理 {i} 个要素...")
    
    # 更新CRS信息
    geojson_data['crs'] = {
        "type": "name",
        "properties": {
            "name": "urn:ogc:def:crs:EPSG::4326"
        }
    }
    
    # 保存转换后的文件
    with open('flood_data_wgs84.geojson', 'w', encoding='utf-8') as f:
        json.dump(geojson_data, f, ensure_ascii=False, indent=2)
    
    print("坐标转换完成！")
    print("转换后的文件保存为: flood_data_wgs84.geojson")
    
    # 显示第一个要素的坐标范围
    first_feature = geojson_data['features'][0]
    coords = first_feature['geometry']['coordinates'][0]
    lons = [c[0] for c in coords]
    lats = [c[1] for c in coords]
    
    print(f"转换后的坐标范围:")
    print(f"经度: {min(lons):.6f} 到 {max(lons):.6f}")
    print(f"纬度: {min(lats):.6f} 到 {max(lats):.6f}")
    
    # 计算中心点
    center_lon = (min(lons) + max(lons)) / 2
    center_lat = (min(lats) + max(lats)) / 2
    print(f"建议的地图中心点: [{center_lon:.6f}, {center_lat:.6f}]")

if __name__ == "__main__":
    try:
        convert_geojson_coordinates()
    except Exception as e:
        print(f"转换过程中出错: {e}")
        print("请确保已安装pyproj库: pip install pyproj")
