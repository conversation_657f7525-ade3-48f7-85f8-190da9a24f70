<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>洪水内涝演进动画</title>
    <meta name="viewport" content="initial-scale=1,maximum-scale=1,user-scalable=no">
    <link href="https://api.mapbox.com/mapbox-gl-js/v3.4.0/mapbox-gl.css" rel="stylesheet">
    <script src="https://api.mapbox.com/mapbox-gl-js/v3.4.0/mapbox-gl.js"></script>
    <style>
        body { margin: 0; padding: 0; }
        #map { position: absolute; top: 0; bottom: 0; width: 100%; }
        .map-overlay {
            font-family: Arial, sans-serif;
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.8);
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 1px 4px rgba(0,0,0,0.2);
            width: 80%;
            max-width: 600px;
        }
        .map-overlay h3 { margin: 0 0 10px; }
        .map-overlay label { display: block; }
        #slider { width: 100%; }
        #time-label {
            font-size: 1.2em;
            font-weight: bold;
            text-align: center;
            margin-top: 5px;
        }
        .legend {
            background-color: #fff;
            border-radius: 3px;
            bottom: 30px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            font: 12px/20px 'Helvetica Neue', Arial, Helvetica, sans-serif;
            padding: 10px;
            position: absolute;
            right: 10px;
            z-index: 1;
        }
        .legend h4 {
            margin: 0 0 10px;
        }
        .legend div span {
            border-radius: 50%;
            display: inline-block;
            height: 10px;
            margin-right: 5px;
            width: 10px;
        }
    </style>
</head>
<body>

<div id="map"></div>

<div class="map-overlay">
    <h3>洪水演进模拟</h3>
    <label>时间轴</label>
    <input id="slider" type="range" min="0" max="100" step="1" value="0">
    <div id="time-label">Loading...</div>
</div>

<div id="legend" class="legend">
    <h4>水深 (m)</h4>
    <div><span style="background-color: #feebe2"></span>0.1</div>
    <div><span style="background-color: #fbb4b9"></span>0.5</div>
    <div><span style="background-color: #f768a1"></span>1.0</div>
    <div><span style="background-color: #c51b8a"></span>2.0</div>
    <div><span style="background-color: #7a0177"></span>&gt; 3.0</div>
</div>

<script>
    // 替换为你的 Mapbox Access Token
    mapboxgl.accessToken = 'pk.eyJ1IjoicmlkZGxlOTExIiwiYSI6ImNqNXVxZGp3aTFyMmIycW4yaHVuenhkY3IifQ.Or4MfHvoQOSmUnB57FCfHQ';

    const map = new mapboxgl.Map({
        container: 'map',
        style: 'mapbox://styles/mapbox/dark-v11', // 推荐使用深色底图
        center: [114.05, 22.54], // TODO: 修改为你的城市中心点经纬度
        zoom: 11 // TODO: 修改为合适的缩放级别
    });

    // 定义水深到颜色的映射关系
    const DEPTH_COLOR_SCALE = [
        'interpolate',
        ['linear'],
        // ['get', propertyName] 将在后面动态设置
        0, 'rgba(0, 0, 0, 0)', // 水深为0时透明
        0.01, '#feebe2',
        0.5, '#fbb4b9',
        1.0, '#f768a1',
        2.0, '#c51b8a',
        3.0, '#7a0177'
    ];

    let timeProperties = []; // 存储所有时间属性名, e.g., 'depth_2025-04-24T01-15'
    let timeLabels = []; // 存储用于显示的时间标签, e.g., '2025-04-24 01:15'

    map.on('load', async () => {
        // 加载处理好的 GeoJSON 数据
        const response = await fetch('./flood_data_prepared.geojson');
        const geojsonData = await response.json();

        // 从第一个 feature 的 properties 中提取所有时间点信息
        const firstFeatureProps = geojsonData.features[0].properties;
        for (const prop in firstFeatureProps) {
            if (prop.startsWith('depth_')) {
                timeProperties.push(prop);
                // 将属性名转换回可读的日期格式
                const readableTime = prop.replace('depth_', '').replace('T', ' ').replace('-',':');
                timeLabels.push(readableTime);
            }
        }
        
        // 按时间排序
        timeProperties.sort();
        timeLabels.sort();

        // 更新时间滑块
        const slider = document.getElementById('slider');
        slider.min = 0;
        slider.max = timeProperties.length - 1;
        slider.value = 0;

        // 添加数据源
        map.addSource('flood-data', {
            type: 'geojson',
            data: geojsonData
        });

        // 获取第一个时间点作为初始渲染
        const initialTimeProperty = timeProperties[0];
        
        // 动态构建颜色表达式
        let colorExpression = [...DEPTH_COLOR_SCALE];
        colorExpression.splice(2, 0, ['get', initialTimeProperty]); // 插入 ['get', 'property_name']

        // 添加图层进行渲染
        map.addLayer({
            id: 'flood-layer',
            type: 'fill',
            source: 'flood-data',
            paint: {
                'fill-color': colorExpression,
                'fill-opacity': 0.75, // 设置透明度
                'fill-outline-color': 'rgba(0,0,0,0.1)' // 网格描边色
            }
        });
        
        // 更新初始时间标签
        document.getElementById('time-label').textContent = timeLabels[0];

        // 监听滑块事件
        slider.addEventListener('input', (e) => {
            const timeIndex = parseInt(e.target.value, 10);
            const currentTimeProperty = timeProperties[timeIndex];
            
            // 动态构建新的颜色表达式
            let newColorExpression = [...DEPTH_COLOR_SCALE];
            newColorExpression.splice(2, 0, ['get', currentTimeProperty]);

            // 使用 setPaintProperty 高效更新图层颜色，无需重新加载数据
            map.setPaintProperty('flood-layer', 'fill-color', newColorExpression);
            
            // 更新时间标签
            document.getElementById('time-label').textContent = timeLabels[timeIndex];
        });
    });
</script>

</body>
</html>