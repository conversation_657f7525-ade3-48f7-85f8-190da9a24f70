import pandas as pd
import geopandas as gpd

# --- 1. 加载数据 ---
# 加载您的 SHP 网格文件
# 请确保您的 SHP 文件中有一个属性列与 CSV 中的 'cell_name' 对应
# 假设该列也叫 'cell_name'
print("Loading shapefile...")
grid_gdf = gpd.read_file("./shp07/50Y24H.2D_cells_-_Copy.shp")

# !!! 新增的调试代码：打印 SHP 文件的所有列名 !!!
print("Shapefile 的所有列名是:", grid_gdf.columns) 

# 加载您的 CSV 数据
print("Loading CSV data...")
# 使用您提供的文件名或实际文件名
depth_df = pd.read_csv("cell_res_5y2h.csv")


try:
    grid_gdf = grid_gdf.rename(columns={'NAME': 'cell_name'})
    print("列名已成功重命名为 'cell_name'")
except KeyError:
    print("错误：请将 'YOUR_COLUMN_NAME' 替换为你真实的列名！")
    # 如果 'cell_name' 本身就存在，则无需重命名，可以注释掉上面一行
    pass

# --- 2. 数据处理与转换 ---
# 为了让时间戳成为合法的属性名（列名），我们对其进行格式化
# 例如: '2025-04-24 1:15' -> 'depth_2025-04-24T01-15'
print("Pivoting time-series data...")
depth_df['time_key'] = 'depth_' + pd.to_datetime(depth_df['time_series']).dt.strftime('%Y-%m-%dT%H-%M')

# 将 "长" 格式的表格转换为 "宽" 格式
# 每个时间点成为一个独立的列
depth_wide_df = depth_df.pivot_table(
    index='cell_name',
    columns='time_key',
    values='cell_depth',
    fill_value=0  # 对于没有数据的网格或时间点，用水深 0 填充
).reset_index()


# --- 3. 合并地理数据和时间序列数据 ---
# 将处理后的宽表格数据合并到 GeoDataFrame 中
# `how='left'` 保证所有地理网格都被保留
print("Merging spatial and temporal data...")
# 注意：确保 grid_gdf['cell_name'] 和 depth_wide_df['cell_name'] 的数据类型一致
grid_gdf['cell_name'] = grid_gdf['cell_name'].astype(depth_wide_df['cell_name'].dtype)

merged_gdf = grid_gdf.merge(depth_wide_df, on='cell_name', how='left')

# 对于没有在CSV中出现的网格，用0填充所有水深列
depth_columns = [col for col in merged_gdf.columns if col.startswith('depth_')]
merged_gdf[depth_columns] = merged_gdf[depth_columns].fillna(0)


# --- 4. 保存为最终的 GeoJSON 文件 ---
# 这个文件将用于 Mapbox 可视化
print("Saving to GeoJSON...")
output_path = "flood_data_prepared.geojson"
merged_gdf.to_file(output_path, driver='GeoJSON')

print(f"Data preparation complete! Output saved to {output_path}")